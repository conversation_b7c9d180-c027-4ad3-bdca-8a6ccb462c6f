# test_mtpi_transitions.py
# Script to test MTPI signal transitions and their effect on trading

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os
import sys
import json

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.MTPI_signal_handler import save_mtpi_signal_to_file, fetch_mtpi_signal, fetch_historical_mtpi_signals
from src.trading.executor import TradingExecutor
from src.config_manager import load_config, get_trading_config
from background_service import BackgroundService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('mtpi_transitions_test.log')
    ]
)

def create_historical_mtpi_signals(output_file, days=30, signal_pattern=None):
    """
    Create a file with historical MTPI signals for testing.
    
    Args:
        output_file: Path to save the signals
        days: Number of days of historical data
        signal_pattern: Optional list of signals to cycle through
    
    Returns:
        DataFrame with the generated signals
    """
    try:
        # Generate dates
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        
        # Generate signals
        if signal_pattern:
            # Cycle through the provided pattern
            signals = [signal_pattern[i % len(signal_pattern)] for i in range(len(dates))]
        else:
            # Default pattern: mostly bullish with some bearish periods
            signals = []
            for i in range(len(dates)):
                if i % 10 < 7:  # 70% bullish
                    signals.append(1)
                else:  # 30% bearish
                    signals.append(-1)
        
        # Create DataFrame
        df = pd.DataFrame({
            'date': dates,
            'signal': signals
        })
        df.set_index('date', inplace=True)
        
        # Save to file
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        df.to_csv(output_file)
        
        logging.info(f"Created historical MTPI signals in {output_file}")
        return df
    
    except Exception as e:
        logging.error(f"Error creating historical MTPI signals: {e}")
        return None

def test_mtpi_transition_sequence():
    """
    Test a sequence of MTPI signal transitions and verify trading behavior.
    """
    logging.info("=" * 80)
    logging.info("TESTING MTPI TRANSITION SEQUENCE")
    logging.info("=" * 80)
    
    # Initialize components
    trading_executor = TradingExecutor('binance')
    
    # Ensure we're in paper trading mode
    trading_config = get_trading_config()
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        trading_executor.set_trading_mode('paper')
    
    # Reset paper trading account
    if hasattr(trading_executor, 'paper_trading'):
        trading_executor.paper_trading.reset_account()
        initial_balance = trading_executor.paper_trading.get_balance()
        logging.info(f"Reset paper trading account. Initial balance: {initial_balance}")
    
    # Define test asset
    test_asset = 'BTC/USDC'
    
    # Define a sequence of MTPI signals to test
    signal_sequence = [1, 1, -1, -1, 1, 1, -1, 1]
    expected_actions = ['buy', 'hold', 'sell', 'hold', 'buy', 'hold', 'sell', 'buy']
    
    results = []
    
    # Run through the sequence
    for i, (signal, expected_action) in enumerate(zip(signal_sequence, expected_actions)):
        logging.info(f"Step {i+1}: Setting MTPI signal to {signal} (Expected action: {expected_action})")
        
        # Set the MTPI signal
        save_mtpi_signal_to_file(signal, 'mtpi_signal.txt')
        
        # Execute the strategy
        result = trading_executor.execute_strategy_signal(test_asset, signal)
        logging.info(f"Execution result: {result}")
        
        # Get current positions
        if trading_config.get('mode') == 'paper':
            positions = trading_executor.paper_trading.get_positions()
            balance = trading_executor.paper_trading.get_balance()
        else:
            positions = trading_executor.account_manager.get_open_positions()
            balance = trading_executor.account_manager.get_balance('USDC')
        
        logging.info(f"Current positions: {positions}")
        logging.info(f"Current balance: {balance}")
        
        # Determine actual action
        if 'action' in result:
            actual_action = result['action']
        elif result.get('success', False):
            if expected_action == 'buy' and test_asset in positions:
                actual_action = 'buy'
            elif expected_action == 'sell' and test_asset not in positions:
                actual_action = 'sell'
            elif expected_action == 'hold':
                actual_action = 'hold'
            else:
                actual_action = 'unknown'
        else:
            actual_action = 'failed'
        
        # Check if action matches expectation
        action_correct = (actual_action == expected_action)
        
        # Store result
        results.append({
            'step': i+1,
            'signal': signal,
            'expected_action': expected_action,
            'actual_action': actual_action,
            'correct': action_correct,
            'has_position': test_asset in positions,
            'balance': balance
        })
        
        # Add a small delay to ensure distinct timestamps
        time.sleep(1)
    
    # Analyze results
    correct_count = sum(1 for r in results if r['correct'])
    success_rate = correct_count / len(results) * 100
    
    logging.info("=" * 80)
    logging.info("MTPI TRANSITION SEQUENCE RESULTS")
    logging.info("=" * 80)
    
    for r in results:
        status = "✓" if r['correct'] else "✗"
        logging.info(f"Step {r['step']}: Signal={r['signal']}, Expected={r['expected_action']}, "
                    f"Actual={r['actual_action']}, {status}")
    
    logging.info(f"Success rate: {success_rate:.1f}% ({correct_count}/{len(results)})")
    
    return success_rate >= 80  # Consider test passed if at least 80% correct

def test_background_service_mtpi_transitions():
    """
    Test the background service's handling of MTPI signal transitions.
    """
    logging.info("=" * 80)
    logging.info("TESTING BACKGROUND SERVICE MTPI TRANSITIONS")
    logging.info("=" * 80)
    
    # Initialize the background service
    service = BackgroundService()
    
    # Ensure trading is enabled in paper mode
    trading_config = get_trading_config()
    if not trading_config.get('enabled', False):
        logging.warning("Trading is disabled. Enabling for test...")
        service.trading_config['enabled'] = True
    
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        service.trading_executor.set_trading_mode('paper')
    
    # Reset paper trading account if possible
    if hasattr(service.trading_executor, 'paper_trading'):
        service.trading_executor.paper_trading.reset_account()
        initial_balance = service.trading_executor.paper_trading.get_balance()
        logging.info(f"Reset paper trading account. Initial balance: {initial_balance}")
    
    # Define a sequence of MTPI signals to test
    signal_sequence = [1, -1, 1, -1]
    
    results = []
    
    # Run through the sequence
    for i, signal in enumerate(signal_sequence):
        logging.info(f"Step {i+1}: Setting MTPI signal to {signal}")
        
        # Set the MTPI signal
        save_mtpi_signal_to_file(signal, 'mtpi_signal.txt')
        service.last_mtpi_signal = signal if i > 0 else None  # Simulate first run vs. subsequent runs
        
        # Execute the strategy
        service.execute_strategy()
        
        # Get current positions
        if service.trading_config.get('mode') == 'paper':
            positions = service.trading_executor.paper_trading.get_positions()
            balance = service.trading_executor.paper_trading.get_balance()
        else:
            positions = service.account_manager.get_open_positions()
            balance = service.account_manager.get_balance('USDC')
        
        logging.info(f"Current positions: {positions}")
        logging.info(f"Current balance: {balance}")
        
        # Determine expected state
        expected_has_positions = (signal == 1)
        actual_has_positions = len(positions) > 0
        state_correct = (expected_has_positions == actual_has_positions)
        
        # Store result
        results.append({
            'step': i+1,
            'signal': signal,
            'expected_has_positions': expected_has_positions,
            'actual_has_positions': actual_has_positions,
            'correct': state_correct,
            'positions': positions,
            'balance': balance
        })
        
        # Add a small delay to ensure distinct timestamps
        time.sleep(1)
    
    # Analyze results
    correct_count = sum(1 for r in results if r['correct'])
    success_rate = correct_count / len(results) * 100
    
    logging.info("=" * 80)
    logging.info("BACKGROUND SERVICE MTPI TRANSITIONS RESULTS")
    logging.info("=" * 80)
    
    for r in results:
        status = "✓" if r['correct'] else "✗"
        logging.info(f"Step {r['step']}: Signal={r['signal']}, "
                    f"Expected positions: {'Yes' if r['expected_has_positions'] else 'No'}, "
                    f"Actual positions: {'Yes' if r['actual_has_positions'] else 'No'}, {status}")
    
    logging.info(f"Success rate: {success_rate:.1f}% ({correct_count}/{len(results)})")
    
    return success_rate >= 75  # Consider test passed if at least 75% correct

if __name__ == "__main__":
    # Run all tests
    transition_result = test_mtpi_transition_sequence()
    background_result = test_background_service_mtpi_transitions()
    
    # Print summary
    logging.info("=" * 80)
    logging.info("TEST RESULTS SUMMARY")
    logging.info("=" * 80)
    logging.info(f"MTPI Transition Sequence Test: {'PASSED' if transition_result else 'FAILED'}")
    logging.info(f"Background Service MTPI Transitions Test: {'PASSED' if background_result else 'FAILED'}")
    
    if transition_result and background_result:
        logging.info("All tests PASSED!")
    else:
        logging.info("Some tests FAILED. Check the logs for details.")
