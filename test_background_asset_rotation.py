# test_background_asset_rotation.py
# Script to test asset rotation handling in the background service

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import os
import sys
import json

# Add the project root to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.MTPI_signal_handler import save_mtpi_signal_to_file
from src.trading.executor import TradingExecutor
from src.config_manager import load_config, get_trading_config
from src.state_manager import load_state, save_state
from background_service import BackgroundService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('asset_rotation_test.log')
    ]
)

def modify_asset_scores(scores_file, new_scores):
    """
    Modify the asset scores file to force a specific asset rotation.
    
    Args:
        scores_file: Path to the scores file
        new_scores: Dictionary of asset scores to write
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(scores_file), exist_ok=True)
        
        # Write the new scores
        with open(scores_file, 'w') as f:
            json.dump(new_scores, f, indent=2)
        
        logging.info(f"Modified asset scores in {scores_file}")
        return True
    except Exception as e:
        logging.error(f"Error modifying asset scores: {e}")
        return False

def test_background_asset_rotation():
    """
    Test the background service's handling of asset rotation.
    This verifies that when the best asset changes, the background service
    correctly exits the current position and enters a new one.
    """
    logging.info("=" * 80)
    logging.info("TESTING BACKGROUND SERVICE ASSET ROTATION")
    logging.info("=" * 80)
    
    # Initialize the background service
    service = BackgroundService()
    
    # Ensure trading is enabled in paper mode
    trading_config = get_trading_config()
    if not trading_config.get('enabled', False):
        logging.warning("Trading is disabled. Enabling for test...")
        # Note: In a real implementation, you would modify the config file
        # For this test, we'll just modify the in-memory config
        service.trading_config['enabled'] = True
    
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        service.trading_executor.set_trading_mode('paper')
    
    # Step 1: Force a bullish MTPI signal
    logging.info("Setting MTPI signal to bullish (1)")
    save_mtpi_signal_to_file(1, 'mtpi_signal.txt')
    service.last_mtpi_signal = 1
    
    # Step 2: Modify asset scores to prioritize BTC
    today = datetime.now().strftime('%Y-%m-%d')
    scores_dir = os.path.join('data', 'scores')
    scores_file = os.path.join(scores_dir, f'scores_{today}.json')
    
    first_asset_scores = {
        "BTC/USDC": 10.0,
        "ETH/USDC": 8.0,
        "SOL/USDC": 7.0,
        "XRP/USDC": 6.0,
        "ADA/USDC": 5.0
    }
    
    modify_asset_scores(scores_file, first_asset_scores)
    
    # Step 3: Run the service once to establish positions
    logging.info("Running background service with BTC as top asset")
    service.execute_strategy()
    
    # Get current positions
    if service.trading_config.get('mode') == 'paper':
        positions_after_first = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_first = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after first execution: {positions_after_first}")
    
    # Step 4: Modify asset scores to prioritize ETH
    second_asset_scores = {
        "ETH/USDC": 12.0,
        "BTC/USDC": 9.0,
        "SOL/USDC": 7.0,
        "XRP/USDC": 6.0,
        "ADA/USDC": 5.0
    }
    
    modify_asset_scores(scores_file, second_asset_scores)
    
    # Step 5: Run the service again
    logging.info("Running background service with ETH as top asset")
    service.execute_strategy()
    
    # Check if positions were rotated
    if service.trading_config.get('mode') == 'paper':
        positions_after_second = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_second = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after second execution: {positions_after_second}")
    
    # Check if rotation was successful
    rotation_success = False
    if positions_after_second:
        # Check if we're now holding ETH and not BTC (or holding more ETH than BTC)
        has_eth = any('ETH/USDC' in pos for pos in positions_after_second)
        has_btc = any('BTC/USDC' in pos for pos in positions_after_second)
        
        if has_eth and (not has_btc or positions_after_second.get('ETH/USDC', 0) > positions_after_second.get('BTC/USDC', 0)):
            logging.info("SUCCESS: Asset rotation in background service completed successfully")
            rotation_success = True
        else:
            logging.error(f"FAIL: Asset rotation in background service failed. Current positions: {positions_after_second}")
    else:
        logging.error("FAIL: No positions after rotation attempt")
    
    return rotation_success

def test_multi_asset_rotation():
    """
    Test the background service's handling of multi-asset rotation.
    This verifies that when using n_assets > 1, the system correctly
    rotates between different sets of assets.
    """
    logging.info("=" * 80)
    logging.info("TESTING MULTI-ASSET ROTATION")
    logging.info("=" * 80)
    
    # Initialize the background service
    service = BackgroundService()
    
    # Ensure trading is enabled in paper mode
    trading_config = get_trading_config()
    if not trading_config.get('enabled', False):
        logging.warning("Trading is disabled. Enabling for test...")
        service.trading_config['enabled'] = True
    
    if trading_config.get('mode') != 'paper':
        logging.warning("Switching to paper trading mode for safety")
        service.trading_executor.set_trading_mode('paper')
    
    # Modify the service config to use multiple assets
    service.config['settings']['n_assets'] = 3
    service.config['settings']['use_weighted_allocation'] = True
    service.config['settings']['weights'] = [0.5, 0.3, 0.2]
    
    # Step 1: Force a bullish MTPI signal
    logging.info("Setting MTPI signal to bullish (1)")
    save_mtpi_signal_to_file(1, 'mtpi_signal.txt')
    service.last_mtpi_signal = 1
    
    # Step 2: Modify asset scores for first set
    today = datetime.now().strftime('%Y-%m-%d')
    scores_dir = os.path.join('data', 'scores')
    scores_file = os.path.join(scores_dir, f'scores_{today}.json')
    
    first_asset_scores = {
        "BTC/USDC": 10.0,
        "ETH/USDC": 9.0,
        "SOL/USDC": 8.0,
        "XRP/USDC": 7.0,
        "ADA/USDC": 6.0
    }
    
    modify_asset_scores(scores_file, first_asset_scores)
    
    # Step 3: Run the service once to establish positions
    logging.info("Running background service with first set of assets")
    service.execute_strategy()
    
    # Get current positions
    if service.trading_config.get('mode') == 'paper':
        positions_after_first = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_first = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after first execution: {positions_after_first}")
    
    # Step 4: Modify asset scores for second set
    second_asset_scores = {
        "XRP/USDC": 12.0,
        "ADA/USDC": 11.0,
        "LTC/USDC": 10.0,
        "BTC/USDC": 9.0,
        "ETH/USDC": 8.0,
        "SOL/USDC": 7.0
    }
    
    modify_asset_scores(scores_file, second_asset_scores)
    
    # Step 5: Run the service again
    logging.info("Running background service with second set of assets")
    service.execute_strategy()
    
    # Check if positions were rotated
    if service.trading_config.get('mode') == 'paper':
        positions_after_second = service.trading_executor.paper_trading.get_positions()
    else:
        positions_after_second = service.account_manager.get_open_positions()
    
    logging.info(f"Positions after second execution: {positions_after_second}")
    
    # Check if rotation was successful
    rotation_success = False
    if positions_after_second:
        # Check if we're now holding the new top assets
        has_xrp = any('XRP/USDC' in pos for pos in positions_after_second)
        has_ada = any('ADA/USDC' in pos for pos in positions_after_second)
        
        if has_xrp and has_ada:
            logging.info("SUCCESS: Multi-asset rotation completed successfully")
            rotation_success = True
        else:
            logging.error(f"FAIL: Multi-asset rotation failed. Current positions: {positions_after_second}")
    else:
        logging.error("FAIL: No positions after rotation attempt")
    
    return rotation_success

if __name__ == "__main__":
    # Run all tests
    asset_rotation_result = test_background_asset_rotation()
    multi_asset_result = test_multi_asset_rotation()
    
    # Print summary
    logging.info("=" * 80)
    logging.info("TEST RESULTS SUMMARY")
    logging.info("=" * 80)
    logging.info(f"Background Asset Rotation Test: {'PASSED' if asset_rotation_result else 'FAILED'}")
    logging.info(f"Multi-Asset Rotation Test: {'PASSED' if multi_asset_result else 'FAILED'}")
    
    if asset_rotation_result and multi_asset_result:
        logging.info("All tests PASSED!")
    else:
        logging.info("Some tests FAILED. Check the logs for details.")
