import os
import random
import shutil
import string
from random import randrange
import time
import requests
from bs4 import BeautifulSoup as BSHTML
import json
from datetime import datetime
from urllib.parse import urljoin, urlparse
import mimetypes

class OKPhotoScraper:
    def __init__(self, base_dir="downloaded_photos"):
        self.base_dir = os.path.join(os.getcwd(), base_dir)
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        })
        self.stats = {"total_downloaded": 0, "failed": 0, "albums": 0}
        self.logged_in = False
        self.setup_directories()
    
    def setup_directories(self):
        """Create base directory and logs"""
        if os.path.exists(self.base_dir):
            backup_dir = f"{self.base_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.move(self.base_dir, backup_dir)
            print(f"Backed up existing photos to: {backup_dir}")
        
        os.makedirs(self.base_dir)
        os.makedirs(os.path.join(self.base_dir, "logs"))
    
    def generate_filename(self, url, extension=None):
        """Generate unique filename with proper extension"""
        if not extension:
            # Try to get extension from URL or content type
            parsed = urlparse(url)
            ext = os.path.splitext(parsed.path)[1]
            extension = ext[1:] if ext else 'jpg'
        
        letters_and_digits = string.ascii_letters + string.digits
        rand_string = ''.join(random.sample(letters_and_digits, 12))
        return f"{rand_string}.{extension}"
    
    def download_image(self, img_url, save_path):
        """Download single image with better error handling"""
        try:
            # Ensure proper URL format
            if img_url.startswith('//'):
                img_url = f"https:{img_url}"
            elif not img_url.startswith('http'):
                img_url = f"https://ok.ru{img_url}"
            
            response = self.session.get(img_url, timeout=15, stream=True)
            response.raise_for_status()
            
            # Check if it's actually an image
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return False, f"Not an image: {content_type}"
            
            # Get proper extension from content type
            extension = mimetypes.guess_extension(content_type)
            if extension:
                extension = extension[1:]  # Remove the dot
                filename = self.generate_filename(img_url, extension)
            else:
                filename = self.generate_filename(img_url)
            
            filepath = os.path.join(save_path, filename)
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Check file size (skip tiny images)
            if os.path.getsize(filepath) < 1024:  # Less than 1KB
                os.remove(filepath)
                return False, "Image too small"
            
            return True, filepath
            
        except Exception as e:
            return False, str(e)
    
    def scrape_page(self, url, album_path):
        """Scrape images from a single OK.ru page"""
        try:
            print(f"  Scraping: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            
            soup = BSHTML(response.text, features="lxml")
            
            # Look for different image selectors
            image_selectors = [
                'img[src*="jpg"]',
                'img[src*="jpeg"]', 
                'img[src*="png"]',
                'img[data-src]',
                '.photo img',
                '.image img'
            ]
            
            images = []
            for selector in image_selectors:
                images.extend(soup.select(selector))
            
            downloaded = 0
            for img in images:
                src = img.get('src') or img.get('data-src')
                if not src:
                    continue
                
                # Filter out small/icon images
                if (len(src) <= 72 and 
                    '-' not in src and 
                    'about:blank' not in src and
                    'icon' not in src.lower() and
                    'avatar' not in src.lower()):
                    
                    success, result = self.download_image(src, album_path)
                    if success:
                        downloaded += 1
                        self.stats["total_downloaded"] += 1
                        print(f"    ✓ Downloaded: {os.path.basename(result)}")
                    else:
                        self.stats["failed"] += 1
                        print(f"    ✗ Failed: {result}")
                    
                    time.sleep(1)  # Reduced delay between images
            
            print(f"  Downloaded {downloaded} images from this page")
            return downloaded
            
        except Exception as e:
            print(f"  Error scraping {url}: {e}")
            self.stats["failed"] += 1
            return 0
    
    def login(self, username, password):
        """Login to Odnoklassniki"""
        try:
            # Get login page
            login_url = "https://ok.ru/dk?cmd=AnonymLogin"
            response = self.session.get(login_url)
            soup = BSHTML(response.text, 'lxml')
            
            # Find login form and required fields
            login_data = {
                'fr.email': username,
                'fr.password': password,
            }
            
            # Look for hidden form fields (CSRF tokens, etc.)
            for input_field in soup.find_all('input', type='hidden'):
                name = input_field.get('name')
                value = input_field.get('value')
                if name and value:
                    login_data[name] = value
            
            # Submit login
            login_response = self.session.post(
                "https://ok.ru/dk?cmd=AnonymLogin", 
                data=login_data,
                allow_redirects=True
            )
            
            # Check if login successful
            if 'feed' in login_response.url or 'profile' in login_response.url:
                self.logged_in = True
                print("✓ Successfully logged in to Odnoklassniki")
                return True
            else:
                print("✗ Login failed - check credentials")
                return False
                
        except Exception as e:
            print(f"Login error: {e}")
            return False
    
    def login_with_cookies(self, cookies_file="ok_cookies.json"):
        """Login using saved cookies"""
        try:
            if os.path.exists(cookies_file):
                with open(cookies_file, 'r') as f:
                    cookies = json.load(f)
                    self.session.cookies.update(cookies)
                
                # Test if cookies are still valid
                test_response = self.session.get("https://ok.ru/feed")
                if test_response.status_code == 200 and 'feed' in test_response.url:
                    self.logged_in = True
                    print("✓ Logged in using saved cookies")
                    return True
            
            print("No valid cookies found")
            return False
        except Exception as e:
            print(f"Cookie login error: {e}")
            return False
    
    def save_cookies(self, cookies_file="ok_cookies.json"):
        """Save session cookies for future use"""
        try:
            cookies_dict = dict(self.session.cookies)
            with open(cookies_file, 'w') as f:
                json.dump(cookies_dict, f)
            print(f"Cookies saved to {cookies_file}")
        except Exception as e:
            print(f"Error saving cookies: {e}")
    
    def process_files(self):
        """Process input files and download photos"""
        # Try to login first
        if not self.logged_in:
            if not self.login_with_cookies():
                username = input("Enter your OK.ru email/phone: ")
                password = input("Enter your password: ")
                if not self.login(username, password):
                    print("Cannot proceed without login!")
                    return
                else:
                    self.save_cookies()
        
        input_files = [f for f in os.listdir() if os.path.isfile(f) and f.endswith('.txt')]
        
        if not input_files:
            print("No .txt files found in current directory!")
            return
        
        current_album = None
        
        for file1 in input_files:
            try:
                print(f"\nProcessing file: {file1}")
                with open(file1, 'r', encoding='utf-8', errors='ignore') as file:
                    for line_num, line in enumerate(file, 1):
                        line = line.strip()
                        if not line:
                            continue
                        
                        if line.startswith('!'):
                            # Create new album
                            album_id = randrange(1000)
                            album_name = line[1:].strip() if len(line) > 1 else f"album_{album_id}"
                            # Clean album name for filesystem
                            album_name = "".join(c for c in album_name if c.isalnum() or c in (' ', '-', '_')).strip()
                            
                            current_album = os.path.join(self.base_dir, f"{album_id:03d}_{album_name}")
                            os.makedirs(current_album, exist_ok=True)
                            self.stats["albums"] += 1
                            print(f"\n📁 Created album: {os.path.basename(current_album)}")
                        
                        elif '/' in line and current_album:
                            # Process URL
                            url = f"https://ok.ru{line}" if not line.startswith('http') else line
                            self.scrape_page(url, current_album)
                            time.sleep(3)  # Delay between pages
                        
                        elif line and not current_album:
                            print(f"  Warning: URL found before album definition at line {line_num}: {line}")
                            
            except Exception as e:
                print(f"Error processing file {file1}: {e}")
        
        self.save_stats()
    
    def save_stats(self):
        """Save download statistics"""
        stats_file = os.path.join(self.base_dir, "logs", f"download_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(stats_file, 'w') as f:
            json.dump({
                **self.stats,
                "timestamp": datetime.now().isoformat(),
                "base_directory": self.base_dir
            }, f, indent=2)
        
        print(f"\n📊 Download Summary:")
        print(f"   Albums created: {self.stats['albums']}")
        print(f"   Images downloaded: {self.stats['total_downloaded']}")
        print(f"   Failed downloads: {self.stats['failed']}")
        print(f"   Stats saved to: {stats_file}")

if __name__ == "__main__":
    scraper = OKPhotoScraper()
    scraper.process_files()
