# mtpi_debug_plot.py
# Script to visualize MTPI signals and PGO values for debugging

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal
from src.data_fetcher import fetch_ohlcv_data
import logging
import argparse
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def fetch_and_calculate_mtpi(timeframe='1d', length=35,
                          upper_threshold=1.1, lower_threshold=-0.58,
                          limit=300, since='2023-10-20'):
    """
    Fetches BTC data and calculates MTPI PGO values and signals.

    Args:
        timeframe: The timeframe for the candles
        length: The period length for PGO calculation
        upper_threshold: The threshold for long signals
        lower_threshold: The threshold for short signals
        limit: The maximum number of candles to fetch
        since: The start date for data fetching

    Returns:
        DataFrame with BTC prices, PGO values, and MTPI signals
    """
    logging.info(f"Fetching BTC data for {timeframe} timeframe from {since}...")

    # Calculate warm-up period in terms of candles
    warmup_period = length * 2
    
    # For smaller timeframes, we need to adjust the start date to account for warmup period
    if timeframe.endswith('h') or timeframe.endswith('m'):
        # Parse the numeric part of the timeframe (e.g., '4h' -> 4)
        if timeframe.endswith('h'):
            # For hour timeframes
            hours_per_candle = int(timeframe[:-1])
            # Calculate how many days worth of candles we need for warmup
            days_for_warmup = (warmup_period * hours_per_candle) / 24
            # Add a buffer of 1 day to be safe
            days_for_warmup = int(days_for_warmup) + 1
        elif timeframe.endswith('m'):
            # For minute timeframes
            minutes_per_candle = int(timeframe[:-1])
            # Calculate how many days worth of candles we need for warmup
            days_for_warmup = (warmup_period * minutes_per_candle) / (24 * 60)
            # Add a buffer of 1 day to be safe
            days_for_warmup = int(days_for_warmup) + 1
        
        # Create a new since date that's earlier to account for warmup
        since_date = datetime.strptime(since, '%Y-%m-%d')
        adjusted_since_date = since_date - timedelta(days=days_for_warmup)
        adjusted_since = adjusted_since_date.strftime('%Y-%m-%d')
        
        logging.info(f"Adjusted start date from {since} to {adjusted_since} to account for {warmup_period} candle warmup period")
        since = adjusted_since

    data_dict = fetch_ohlcv_data(
        exchange_id='binance',
        symbols=['BTC/USDT'],
        timeframe=timeframe,
        since=since,
        limit=limit
    )

    if not data_dict or 'BTC/USDT' not in data_dict:
        logging.error("Failed to fetch BTC data")
        return None

    btc_df = data_dict['BTC/USDT']

    # Calculate PGO values
    pgo_values = calculate_pgo(
        df=btc_df,
        length=length
    )

    # Calculate PGO signals
    # Set skip_warmup to False to ensure signals are generated from the beginning
    pgo_signals = generate_pgo_signal(
        df=btc_df,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        skip_warmup=False
    )

    # Create result DataFrame
    result_df = pd.DataFrame({
        'date': btc_df.index,
        'open': btc_df['open'],
        'high': btc_df['high'],
        'low': btc_df['low'],
        'close': btc_df['close'],
        'pgo_value': pgo_values,
        'mtpi_signal': pgo_signals
    })

    logging.info(f"Calculated MTPI data for {len(result_df)} candles")

    return result_df

def plot_mtpi_debug(timeframe='1d', length=35,
                  upper_threshold=1.1, lower_threshold=-0.58,
                  limit=300, since='2023-10-20'):
    """
    Plots BTC price, PGO values, and MTPI signals for visual debugging.

    Args:
        timeframe: The timeframe for the candles
        length: The period length for PGO calculation
        upper_threshold: The threshold for long signals
        lower_threshold: The threshold for short signals
        limit: The maximum number of candles to fetch
        since: The start date for data fetching
    """
    df = fetch_and_calculate_mtpi(
        timeframe=timeframe,
        length=length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        limit=limit,
        since=since
    )

    if df is None or df.empty:
        logging.error("No data to plot")
        return

    # Set up the plot style to mimic TradingView dark theme
    plt.style.use('dark_background')

    # Create figure with subplots using GridSpec for better control
    fig = plt.figure(figsize=(14, 10))
    gs = plt.GridSpec(2, 1, height_ratios=[3, 1])

    # Price chart subplot
    ax1 = plt.subplot(gs[0])
    ax2 = plt.subplot(gs[1], sharex=ax1)

    # Calculate percentage of each signal type for title
    signal_counts = df['mtpi_signal'].value_counts()
    total_signals = len(df)
    bullish_pct = signal_counts.get(1, 0) / total_signals * 100
    bearish_pct = signal_counts.get(-1, 0) / total_signals * 100
    neutral_pct = signal_counts.get(0, 0) / total_signals * 100

    # Set background color
    fig.patch.set_facecolor('#121212')
    ax1.set_facecolor('#121212')
    ax2.set_facecolor('#121212')

    # Format dates on x-axis
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b %y'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=1))

    # Set grid style
    ax1.grid(True, linestyle='--', alpha=0.2, color='gray')
    ax2.grid(True, linestyle='--', alpha=0.2, color='gray')

    # Plot price chart with colored candlesticks based on signal
    for i in range(len(df)):
        # Get data for this bar
        date = df['date'].iloc[i]
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        signal = df['mtpi_signal'].iloc[i]

        # Determine color based on signal
        color = '#0EFF7A' if signal == 1 else '#F10A3C' if signal == -1 else '#808080'

        # Plot candlestick
        # Body
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)

        # Plot the body as a rectangle
        if body_height > 0:
            ax1.add_patch(plt.Rectangle(
                (mdates.date2num(date) - 0.4, body_bottom),
                0.8, body_height,
                fill=True, color=color, alpha=0.8
            ))

        # Wick
        ax1.plot([mdates.date2num(date), mdates.date2num(date)],
                [low_price, high_price],
                color=color, linewidth=1)

    # Plot PGO Values with thresholds
    ax2.plot(df['date'], df['pgo_value'], color='#3A7BD5', linewidth=1.5, label='PGO Value')
    ax2.axhline(y=upper_threshold, color='#808080', linestyle='--', alpha=0.7, label=f'Upper Threshold ({upper_threshold})')
    ax2.axhline(y=lower_threshold, color='#808080', linestyle='--', alpha=0.7, label=f'Lower Threshold ({lower_threshold})')
    ax2.axhline(y=0, color='#808080', linestyle='-', alpha=0.5)

    # Fill areas above/below thresholds
    ax2.fill_between(df['date'], df['pgo_value'], upper_threshold,
                    where=(df['pgo_value'] > upper_threshold),
                    color='#0EFF7A', alpha=0.3)
    ax2.fill_between(df['date'], df['pgo_value'], lower_threshold,
                    where=(df['pgo_value'] < lower_threshold),
                    color='#F10A3C', alpha=0.3)

    # Set titles and labels
    title = f'BTC/USDT with MTPI Signal ({timeframe} Timeframe, Since {since})\n'
    title += f'Signal Distribution: Bullish {bullish_pct:.1f}%, Bearish {bearish_pct:.1f}%, Neutral {neutral_pct:.1f}%'
    ax1.set_title(title, fontsize=14, color='white')
    ax1.set_ylabel('Price (USDT)', fontsize=12, color='white')

    ax2.set_title(f'PGO Indicator (Length: {length}, Thresholds: {upper_threshold}/{lower_threshold})',
                 fontsize=12, color='white')
    ax2.set_ylabel('PGO Value', fontsize=10, color='white')
    ax2.set_xlabel('Date', fontsize=12, color='white')

    # Style the tick labels
    ax1.tick_params(axis='both', colors='white')
    ax2.tick_params(axis='both', colors='white')

    # Rotate x-axis labels for better readability
    plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')

    # Add legend for the PGO signals
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#0EFF7A', edgecolor='#0EFF7A', label='Long Signal (PGO > 1.1)'),
        Patch(facecolor='#F10A3C', edgecolor='#F10A3C', label='Short Signal (PGO < -0.58)')
    ]
    ax1.legend(handles=legend_elements, loc='upper left', framealpha=0.7)

    # Add info about calculations
    pgo_info = (
        f"PGO Calculation: (Close - SMA(Close, {length})) / ATR({length})\n"
        f"Upper Threshold: {upper_threshold}, Lower Threshold: {lower_threshold}\n"
        f"Bullish Signal (+1): PGO crosses above {upper_threshold}\n"
        f"Bearish Signal (-1): PGO crosses below {lower_threshold}\n"
        f"Neutral Signal (0): PGO between thresholds without crossing"
    )
    fig.text(0.12, 0.01, pgo_info, fontsize=10, color='white', bbox=dict(facecolor='#333333', alpha=0.8))

    # Adjust layout and show plot
    plt.tight_layout()
    plt.subplots_adjust(top=0.9, bottom=0.12)

    # Save the plot to a file
    plot_filename = f"mtpi_debug_{timeframe}_since_{since.replace('-', '')}.png"
    plt.savefig(plot_filename, dpi=150, facecolor=fig.get_facecolor())
    logging.info(f"Plot saved to {plot_filename}")

    # Show the plot
    plt.show()

def compare_timeframes(since='2023-10-20'):
    """
    Compares MTPI signals and PGO values across multiple timeframes.

    Args:
        since: The start date for data fetching
    """
    timeframes = ['1d', '4h', '1h']
    limit = 1000  # Increase limit to get enough data since the specified start date
    length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58

    # Fetch data for each timeframe
    data = {}
    for tf in timeframes:
        data[tf] = fetch_and_calculate_mtpi(
            timeframe=tf,
            length=length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            limit=limit,
            since=since
        )

    # Set up the plot style to mimic TradingView dark theme
    plt.style.use('dark_background')

    # Create figure with subplots - one row per timeframe, two columns
    fig, axes = plt.subplots(len(timeframes), 2, figsize=(16, 5*len(timeframes)))

    # Set background color for the figure
    fig.patch.set_facecolor('#121212')

    for i, tf in enumerate(timeframes):
        df = data[tf]
        if df is None or df.empty:
            continue

        # Signal counts for this timeframe
        signal_counts = df['mtpi_signal'].value_counts()
        total_signals = len(df)
        bullish_pct = signal_counts.get(1, 0) / total_signals * 100
        bearish_pct = signal_counts.get(-1, 0) / total_signals * 100

        # Set background color for each subplot
        axes[i, 0].set_facecolor('#121212')
        axes[i, 1].set_facecolor('#121212')

        # Set grid style
        axes[i, 0].grid(True, linestyle='--', alpha=0.2, color='gray')
        axes[i, 1].grid(True, linestyle='--', alpha=0.2, color='gray')

        # Left plot: BTC Price with colored candlesticks
        # Plot price chart with colored candlesticks based on signal
        for j in range(len(df)):
            # Get data for this bar
            date = df['date'].iloc[j]
            open_price = df['open'].iloc[j]
            close_price = df['close'].iloc[j]
            high_price = df['high'].iloc[j]
            low_price = df['low'].iloc[j]
            signal = df['mtpi_signal'].iloc[j]

            # Determine color based on signal
            color = '#0EFF7A' if signal == 1 else '#F10A3C' if signal == -1 else '#808080'

            # Plot candlestick
            # Body
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)

            # Plot the body as a rectangle
            if body_height > 0:
                axes[i, 0].add_patch(plt.Rectangle(
                    (mdates.date2num(date) - 0.4, body_bottom),
                    0.8, body_height,
                    fill=True, color=color, alpha=0.8
                ))

            # Wick
            axes[i, 0].plot([mdates.date2num(date), mdates.date2num(date)],
                    [low_price, high_price],
                    color=color, linewidth=1)

        # Set title and labels for price chart
        axes[i, 0].set_title(f'{tf} BTC/USDT - Bullish: {bullish_pct:.1f}%, Bearish: {bearish_pct:.1f}%',
                            color='white', fontsize=12)
        axes[i, 0].set_ylabel('Price (USDT)', color='white', fontsize=10)

        # Right plot: PGO Values
        axes[i, 1].plot(df['date'], df['pgo_value'], color='#3A7BD5', linewidth=1.5)
        axes[i, 1].axhline(y=upper_threshold, color='#808080', linestyle='--', alpha=0.7)
        axes[i, 1].axhline(y=lower_threshold, color='#808080', linestyle='--', alpha=0.7)
        axes[i, 1].axhline(y=0, color='#808080', linestyle='-', alpha=0.5)

        # Fill areas above/below thresholds
        axes[i, 1].fill_between(df['date'], df['pgo_value'], upper_threshold,
                            where=(df['pgo_value'] > upper_threshold),
                            color='#0EFF7A', alpha=0.3)
        axes[i, 1].fill_between(df['date'], df['pgo_value'], lower_threshold,
                            where=(df['pgo_value'] < lower_threshold),
                            color='#F10A3C', alpha=0.3)

        axes[i, 1].set_title(f'{tf} PGO Values', color='white', fontsize=12)
        axes[i, 1].set_ylabel('PGO Value', color='white', fontsize=10)

        # Style the tick labels
        axes[i, 0].tick_params(axis='both', colors='white')
        axes[i, 1].tick_params(axis='both', colors='white')

        # Format dates
        for j in range(2):
            axes[i, j].xaxis.set_major_formatter(mdates.DateFormatter('%b %y'))
            axes[i, j].xaxis.set_major_locator(mdates.MonthLocator(interval=1))

    # Add overall title
    fig.suptitle(f'MTPI Analysis Comparison Across Timeframes (Since {since})\nPGO Length: {length}, Upper: {upper_threshold}, Lower: {lower_threshold}',
                fontsize=16, color='white')

    # Add legend for the PGO signals
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#0EFF7A', edgecolor='#0EFF7A', label='Long Signal (PGO > 1.1)'),
        Patch(facecolor='#F10A3C', edgecolor='#F10A3C', label='Short Signal (PGO < -0.58)')
    ]
    fig.legend(handles=legend_elements, loc='lower center', ncol=2, framealpha=0.7, fontsize=10)

    # Save the comparison plot
    plt.tight_layout()
    plt.subplots_adjust(top=0.92, bottom=0.08)
    comparison_filename = f"mtpi_timeframe_comparison_since_{since.replace('-', '')}.png"
    plt.savefig(comparison_filename, dpi=150, facecolor=fig.get_facecolor())
    logging.info(f"Comparison plot saved to {comparison_filename}")

    # Show the plot
    plt.show()

if __name__ == "__main__":
    # Set up argparse to parse command line arguments
    parser = argparse.ArgumentParser(description='Debug MTPI signals with visualization')
    parser.add_argument('--timeframe', default='1d', help='Timeframe to use (e.g., 1d, 4h, 1h)')
    parser.add_argument('--since', default='2023-10-20', help='Start date for analysis (YYYY-MM-DD)')
    parser.add_argument('--upper', type=float, default=1.1, help='Upper threshold for PGO')
    parser.add_argument('--lower', type=float, default=-0.58, help='Lower threshold for PGO')
    parser.add_argument('--length', type=int, default=35, help='PGO period length')
    parser.add_argument('--limit', type=int, default=300, help='Maximum number of candles to fetch')
    args = parser.parse_args()
    
    # Run the plot function with the provided arguments
    plot_mtpi_debug(
        timeframe=args.timeframe,
        length=args.length,
        upper_threshold=args.upper,
        lower_threshold=args.lower,
        limit=args.limit,
        since=args.since
    )