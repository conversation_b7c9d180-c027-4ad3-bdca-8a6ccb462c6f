#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
BTC PGO Visualizer

This script fetches BTC data, calculates the PGO indicator,
and visualizes the price chart with the indicator.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import matplotlib.dates as mdates
from datetime import datetime
import ccxt
import logging
import sys
import os

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.indicators.base_indicators import calculate_sma, calculate_atr
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def plot_btc_pgo(
    df: pd.DataFrame,
    pgo_length: int = 35,
    upper_threshold: float = 1.1,
    lower_threshold: float = -0.58,
    output_file: str = None
):
    """
    Plots BTC price chart with PGO indicator in a TradingView-like style.

    Args:
        df: DataFrame with OHLCV data
        pgo_length: The period length for PGO calculation
        upper_threshold: The threshold for long signals
        lower_threshold: The threshold for short signals
        output_file: Path to save the plot (if None, display instead)
    """
    # Calculate PGO
    pgo_values = calculate_pgo(df, length=pgo_length)

    # Generate PGO signal
    pgo_signal = generate_pgo_signal(
        df,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )

    # Set up the plot style to mimic TradingView dark theme
    plt.style.use('dark_background')

    # Create figure and subplots
    fig = plt.figure(figsize=(14, 10))
    gs = gridspec.GridSpec(2, 1, height_ratios=[3, 1])

    # Price chart subplot
    ax1 = plt.subplot(gs[0])
    ax2 = plt.subplot(gs[1], sharex=ax1)

    # Format dates on x-axis
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b %y'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))

    # Set grid style
    ax1.grid(True, linestyle='--', alpha=0.2, color='gray')
    ax2.grid(True, linestyle='--', alpha=0.2, color='gray')

    # Set background color
    fig.patch.set_facecolor('#121212')
    ax1.set_facecolor('#121212')
    ax2.set_facecolor('#121212')

    # Plot price chart with colored bars based on signal
    for i in range(len(df)):
        # Get data for this bar
        date = df.index[i]
        open_price = df['open'].iloc[i]
        close_price = df['close'].iloc[i]
        high_price = df['high'].iloc[i]
        low_price = df['low'].iloc[i]
        signal = pgo_signal.iloc[i]

        # Determine color based on signal
        color = '#0EFF7A' if signal == 1 else '#F10A3C' if signal == -1 else '#808080'

        # Plot candlestick
        # Body
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)

        # Plot the body as a rectangle
        if body_height > 0:
            ax1.add_patch(plt.Rectangle(
                (mdates.date2num(date) - 0.4, body_bottom),
                0.8, body_height,
                fill=True, color=color, alpha=0.8
            ))

        # Wick
        ax1.plot([mdates.date2num(date), mdates.date2num(date)],
                [low_price, high_price],
                color=color, linewidth=1)

    # Plot PGO indicator
    ax2.plot(df.index, pgo_values, color='#3A7BD5', linewidth=1.5)

    # Add horizontal lines for thresholds
    ax2.axhline(y=upper_threshold, color='#808080', linestyle='--', alpha=0.7)
    ax2.axhline(y=lower_threshold, color='#808080', linestyle='--', alpha=0.7)
    ax2.axhline(y=0, color='#808080', linestyle='-', alpha=0.5)

    # Fill areas above/below thresholds
    ax2.fill_between(df.index, pgo_values, upper_threshold,
                    where=(pgo_values > upper_threshold),
                    color='#0EFF7A', alpha=0.3)
    ax2.fill_between(df.index, pgo_values, lower_threshold,
                    where=(pgo_values < lower_threshold),
                    color='#F10A3C', alpha=0.3)

    # Set titles and labels
    ax1.set_title('BTC/USDT Price Chart with PGO Signal', fontsize=14, color='white')
    ax1.set_ylabel('Price (USDT)', fontsize=12, color='white')

    ax2.set_title(f'PGO Indicator (Length: {pgo_length}, Thresholds: {upper_threshold}/{lower_threshold})',
                 fontsize=12, color='white')
    ax2.set_ylabel('PGO Value', fontsize=10, color='white')
    ax2.set_xlabel('Date', fontsize=12, color='white')

    # Style the tick labels
    ax1.tick_params(axis='both', colors='white')
    ax2.tick_params(axis='both', colors='white')

    # Rotate x-axis labels for better readability
    plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')

    # Add legend for the PGO signals
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#0EFF7A', edgecolor='#0EFF7A', label='Long Signal (PGO > 1.1)'),
        Patch(facecolor='#F10A3C', edgecolor='#F10A3C', label='Short Signal (PGO < -0.58)')
    ]
    ax1.legend(handles=legend_elements, loc='upper left', framealpha=0.7)

    # Adjust layout
    plt.tight_layout()

    # Save or display the plot
    if output_file:
        plt.savefig(output_file, facecolor=fig.get_facecolor(), dpi=100)
        logging.info(f"Saved plot to {output_file}")
    else:
        plt.show()

    plt.close(fig)

def log_signal_transitions(df, pgo_signal):
    """
    Identify and log all signal transitions (entries and exits).

    Args:
        df: DataFrame with OHLCV data
        pgo_signal: Series with PGO signals (1, -1, 0)
    """
    # Initialize tracking variables
    current_signal = pgo_signal.iloc[0]
    current_start = df.index[0]

    print("\n" + "="*80)
    print("BTC PGO SIGNAL ENTRY AND EXIT DATES")
    print("="*80)

    # Print initial state
    initial_state = "IN MARKET" if current_signal == 1 else "OUT OF MARKET"
    print(f"\nInitial state on {current_start.strftime('%Y-%m-%d')}: {initial_state} (Signal: {current_signal})")

    # Find and print all transitions
    print("\nSignal Transitions:")
    print(f"{'Type':<10} {'Date':<12} {'From':<8} {'To':<8}")
    print(f"{'-'*10} {'-'*12} {'-'*8} {'-'*8}")

    transition_count = 0

    for i in range(1, len(pgo_signal)):
        if pgo_signal.iloc[i] != current_signal:
            # Signal changed, record the transition
            transition_date = df.index[i]
            from_signal = current_signal
            to_signal = pgo_signal.iloc[i]

            # Determine transition type
            if from_signal != 1 and to_signal == 1:
                transition_type = "ENTRY"
            elif from_signal == 1 and to_signal != 1:
                transition_type = "EXIT"
            else:
                transition_type = "CHANGE"

            # Print the transition
            print(f"{transition_type:<10} {transition_date.strftime('%Y-%m-%d'):<12} {from_signal:<8} {to_signal:<8}")

            # Update current signal
            current_signal = to_signal
            transition_count += 1

    # Print summary
    print(f"\nTotal transitions: {transition_count}")

    # Calculate market exposure
    in_market_days = sum(1 for s in pgo_signal if s == 1)
    total_days = len(pgo_signal)
    market_exposure = (in_market_days / total_days) * 100

    print(f"\nMarket exposure: {in_market_days} out of {total_days} days ({market_exposure:.1f}%)")

    # Count entries and exits
    entries = sum(1 for i in range(1, len(pgo_signal)) if pgo_signal.iloc[i-1] != 1 and pgo_signal.iloc[i] == 1)
    exits = sum(1 for i in range(1, len(pgo_signal)) if pgo_signal.iloc[i-1] == 1 and pgo_signal.iloc[i] != 1)

    print(f"Number of entries: {entries}")
    print(f"Number of exits: {exits}")
    print("="*80)

def main():
    """Main function to run the visualization"""
    # Configuration
    exchange_id = 'binance'
    symbol = 'BTC/USDT'
    timeframe = '1d'
    start_date = '2023-10-20'  # Starting from the analysis start date
    pgo_length = 35
    upper_threshold = 1.1
    lower_threshold = -0.58
    output_file = 'btc_pgo_visualization_recent.png'

    # Fetch data
    logging.info(f"Fetching BTC data from {start_date}")
    data_dict = fetch_ohlcv_data(
        exchange_id=exchange_id,
        symbols=[symbol],
        timeframe=timeframe,
        since=start_date
    )

    if not data_dict or symbol not in data_dict:
        logging.error("Failed to fetch BTC data. Exiting.")
        return

    # Get the BTC DataFrame
    btc_df = data_dict[symbol]

    # Calculate PGO signal
    pgo_signal = generate_pgo_signal(
        df=btc_df,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )

    # Log signal transitions (entries and exits)
    log_signal_transitions(btc_df, pgo_signal)

    # Create visualization
    plot_btc_pgo(
        df=btc_df,
        pgo_length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold,
        output_file=output_file
    )

    logging.info("Visualization complete!")

if __name__ == "__main__":
    main()
