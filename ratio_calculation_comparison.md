# Comparison of Ratio Calculations: PineScript vs Python

## Sharpe Ratio

### PineScript Implementation
```pine
// From Logic_in_pinescript.pine
sharpe_ratio = math.round((mean_all / stddev_all) * math.sqrt(255), 3)
```

### Python Implementation
```python
# From src/performance.py
def calculate_sharpe_ratio(daily_returns: pd.Series, risk_free_rate: float = 0.0) -> Optional[float]:
    """
    Calculates the annualized Sharpe ratio.
    """
    mean_return = calculate_mean_returns(daily_returns) # Already annualized
    std_dev = calculate_std_dev(daily_returns) # Already annualized

    if mean_return is None or std_dev is None or std_dev == 0:
        return None

    # Calculate annualized excess return
    annualized_excess_return = mean_return - risk_free_rate

    return annualized_excess_return / std_dev
```

### Differences
1. **Risk-Free Rate**: The PineScript implementation doesn't subtract a risk-free rate, while the Python implementation does.
2. **Annualization Factor**: PineScript uses `math.sqrt(255)` for annualization, while Python annualizes both the mean return and standard deviation using `TRADING_DAYS_PER_YEAR = 252`.

## Sortino Ratio

### PineScript Implementation
```pine
// From Logic_in_pinescript.pine
sortino_ratio = math.round((mean_all / stddev_neg) * math.sqrt(255), 3)
```

### Python Implementation
```python
# From src/performance.py
def calculate_sortino_ratio(daily_returns: pd.Series, risk_free_rate: float = 0.0) -> Optional[float]:
    """
    Calculates the annualized Sortino ratio using downside deviation.
    """
    mean_return = calculate_mean_returns(daily_returns) # Already annualized
    if mean_return is None or len(daily_returns) < 2:
        return None

    # Adjust risk-free rate to daily for target return comparison
    daily_target_return = (1 + risk_free_rate)**(1/TRADING_DAYS_PER_YEAR) - 1

    # Calculate downside deviation (std dev of returns below the target)
    downside_returns = daily_returns[daily_returns < daily_target_return]

    if downside_returns.empty or len(downside_returns) < 2:
         return None

    downside_deviation = downside_returns.std(ddof=1) * np.sqrt(TRADING_DAYS_PER_YEAR)

    if downside_deviation == 0:
        return None

    # Calculate annualized excess return over the risk-free rate
    annualized_excess_return = mean_return - risk_free_rate

    return annualized_excess_return / downside_deviation
```

### Differences
1. **Risk-Free Rate**: The PineScript implementation doesn't use a risk-free rate, while the Python implementation does.
2. **Downside Deviation Calculation**: 
   - PineScript uses the standard deviation of all negative returns
   - Python uses the standard deviation of returns below the risk-free rate (or 0 if risk-free rate is 0)
3. **Annualization Factor**: Similar to Sharpe ratio, different annualization approaches.

## Omega Ratio

### PineScript Implementation
```pine
// From Logic_in_pinescript.pine
omega_ratio = math.round(pos_sum / math.abs(neg_sum), 3)
```

### Python Implementation
```python
# From src/performance.py
def calculate_omega_ratio(daily_returns: pd.Series, required_return: float = 0.0) -> Optional[float]:
    """
    Calculates the Omega ratio.
    """
    # Convert annualized required return to daily
    daily_required_return = (1 + required_return)**(1/TRADING_DAYS_PER_YEAR) - 1

    # Calculate sum of gains above the threshold and sum of losses below the threshold
    gains = (daily_returns - daily_required_return)[daily_returns > daily_required_return].sum()
    losses = (daily_required_return - daily_returns)[daily_returns < daily_required_return].sum()

    if losses == 0:
        return np.inf if gains > 0 else None

    return gains / losses
```

### Differences
1. **Threshold**: 
   - PineScript simply divides the sum of positive returns by the absolute sum of negative returns
   - Python uses a required return threshold (default 0) and compares returns against it
2. **Annualization**: The Python implementation converts the annualized required return to daily, while PineScript doesn't have this concept.

## Recommendations for Alignment

To align the calculations:

1. **Sharpe Ratio**: 
   - Ensure both use the same annualization factor (252 trading days is standard)
   - Decide whether to include risk-free rate in both implementations

2. **Sortino Ratio**:
   - Align the downside deviation calculation method
   - Use the same threshold for determining downside returns

3. **Omega Ratio**:
   - Use the same threshold approach in both implementations

The most significant differences appear to be in how downside risk is calculated for the Sortino ratio and how the threshold is applied in the Omega ratio.
